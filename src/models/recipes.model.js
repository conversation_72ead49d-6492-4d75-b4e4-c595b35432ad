import { isValidImageUrl } from "../utils/is-valid-image-url.js";
import { BADGE_TYPE } from "../components/badge/badge-type.js";
import publishedIcon from '@/assets/images/green-arrow-published.png';
import unpublishedIcon from "@/assets/images/unpublished-icon.png";
import updatingIcon from "@/assets/images/updating-icon.png";

export const getTableColumnKeys = ({ showSelectColumn, finalAvailableLangs }) => {
  return [
    showSelectColumn ? 'select' : undefined,
    'image',
    'isin',
    'title',
    finalAvailableLangs.length > 1 ? 'variants' : undefined,
    'modified',
    'externalId',
    'status',
    showSelectColumn ? undefined : 'actions',
  ].filter((item) => item !== undefined);
};

export const getTableColumnNames = ({ showSelectColumn, finalAvailableLangs, t }) => {
  return [
    showSelectColumn ? '' : undefined,
    '',
    t('PAGE.RECIPES.TABLE.HEAD_ISIN'),
    t('PAGE.RECIPES.TABLE.HEAD_TITLE'),
    finalAvailableLangs?.length > 1 ? t('PAGE.RECIPES.TABLE.HEAD_VARIANTS') : undefined,
    t('PAGE.RECIPES.TABLE.HEAD_MODIFIED'),
    t('PAGE.RECIPES.TABLE.HEAD_EXTERNAL_ID'),
    t('PAGE.RECIPES.TABLE.HEAD_STATUS'),
    showSelectColumn ? undefined : '',
  ].filter((item) => item !== undefined);
};

export const RecipesModel = (data, lang) => {
  const {
    isin,
    media,
    title,
    subtitle,
    lastMod,
    lastPublishDate,
    externalId,
    state,
    status,
    langs,
    provider,
  } = data;

  const getMediaUrl = (mediaData) => {
    const media = mediaData?.[lang];
    if (media) {
      if (media.image && (!media.externalImageUrl || media.externalImageUrl === null || media.externalImageUrl === "null")) {
        return media.image;
      } else if (media.externalImageUrl && isValidImageUrl(media.externalImageUrl)) {
        return media.externalImageUrl;
      }
    }
    return null;
  };

  const getBadge = () => {
    if (status === 'active' && state === 'published') {
      return {
        tKey: "COMMON.PUBLISHED",
        badgeType: BADGE_TYPE.LIGHT_GREEN,
        icon: publishedIcon,
      };
    }

    if (state === 'pending' && !lastPublishDate || state === 'pending') {
      return {
        tKey: "COMMON.PENDING",
        badgeType: BADGE_TYPE.YELLOW,
        icon: unpublishedIcon,
      };
    }

    if (state === 'preview') {
      return {
        tKey: "COMMON.PREVIEW",
        badgeType: BADGE_TYPE.YELLOW,
        icon: "",
      };
    }

    if (state === 'failed') {
      return {
        tKey: "COMMON.FAILED",
        badgeType: BADGE_TYPE.RED,
        icon: "",
      };
    }

    if (state === 'publishing') {
      return {
        tKey: "UPDATING",
        badgeType: BADGE_TYPE.BLUE,
        icon: updatingIcon,
      };
    }

    if (state === 'timeout') {
      return {
        tKey: "COMMON.TIMEOUT",
        badgeType: BADGE_TYPE.YELLOW,
        icon: "",
      };
    }

    return {
      tKey: "COMMON.UNPUBLISHED",
      badgeType: BADGE_TYPE.SILVER,
      icon: unpublishedIcon,
    };
  };

  return {
    image: getMediaUrl(media),
    isin,
    title: title?.[lang] || "",
    subtitle: subtitle?.[lang] || "",
    lastMod,
    externalId,
    state,
    status,
    langs,
    schedule: null,
    badge: getBadge(),
    provider,
    usedInHero: false,
  };
}

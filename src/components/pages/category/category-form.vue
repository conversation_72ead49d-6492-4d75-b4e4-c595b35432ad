<template>
  <content-wrapper wrapper-classes="category-item font-family-averta" :is-body-loading="props.isLoading">
    <page-top-block
      :hint="hintID"
      :page-actions-back-label="$t('CATEGORY.BACK_MESSAGE')"
      :page-actions-back-path="'/category'"
      :background-image="image"
      :page-actions-is-continue-disabled="!isCategoryValid || (isEdit && !hasChanges)"
      @page-actions-continue="openPreviewSaveModal"
      @page-actions-cancel="() => navigateTo('/category')"
    >
      <div class="category-item-head">
        <image-box
          :image-src="image"
          :loaded-image-size="loadedImageSize"
          :uploadImagePercentage="uploadImagePercentage"
          @uploadedFile="uploadFile"
        />
        <div class="category-item-details">
          <div class="category-item-details-top border-bottom-dashed">
            <name-field
              v-model:name="categoriesName"
              input-id="category-name"
              :input-placeholder="'Name your category'"
            />

            <div
              class="publish-btn-wrapper"
              :class="{
                'simple-data-tooltip': categoriesState !== 'published',
              }"
              :data-tooltip-text="categoriesState !== 'published' && 'Category must be published to enable toggle'"
            >
              <button-with-switcher
                class="category-item-details-publish-btn"
                :label="$t('COMMON.PUBLISH')"
                :is-checked="isCategoriesStatus === 'active'"
                :is-disabled="categoriesState !== 'published'"
                @action="categoriesName.length > 0 ? publishToggleBtnAsync() : publishToggleBtnPopup()"
              />
            </div>
          </div>

      <div class="category-item-settings">
        <div class="category-item-settings-container">
          <div class="category-item-settings-name">
            <span class="font-size-base font-bold color-black-mine-shaft">Slug:</span>
          </div>
          <div class="category-item-settings-actions border-bottom-dashed">
            <name-field
              v-model:name="categoriesSlug"
              input-id="slug-category"
              @input="handleSlugInput"
              :isRequired="false"
            />
            <div v-if="hasSlugExist" class="slug-exist-main">
              <p class="slug-exist text-light-h4">This slug already exists</p>
            </div>
          </div>
        </div>
      </div>

          <div class="category-item-details-body">
            <div class="category-item-details-text font-size-base">
              <span class="font-bold color-grey">Category Image:</span>
              <span class="font-normal color-grey">jpg,png format (recom. 1MB, max 15 MB)</span>
              <span class="category-item-details-text-mark color-red">*</span>
            </div>
            <div
              v-if="props.isEdit"
              class="delete-btn"
              :class="{
                'simple-data-tooltip simple-data-tooltip-warn': recipeDataForCategories.length || categoryPromotedRecipes.length,
              }"
            >
              <div class="simple-data-tooltip-content" v-if="recipeDataForCategories.length || categoryPromotedRecipes.length">
                <img src="@/assets/images/info.svg?skipsvgo=true" alt="info-icon" class="tooltip-icon" />
                <span>{{ $t('CATEGORY.CATEGORIES_DELETE_INFO') }}</span>
              </div>
              <button
                type="button"
                class="btn-reset"
                @click="deleteCategory"
                :class="{
                  'simple-data-tooltip': isCategoryIncludeInHero
                }"
                :disabled="isCategoryIncludeInHero || recipeDataForCategories.length > 0 || categoryPromotedRecipes.length > 0"
                :data-tooltip-text="isCategoryIncludeInHero && $t('ARTICLE_IN_HERO')"
              >
                <img
                  alt="delete-icon"
                  class="image"
                  :class="{'disabled': isCategoryIncludeInHero || recipeDataForCategories.length > 0 || categoryPromotedRecipes.length > 0}"
                  src="@/assets/images/delete-icon.png"
                />
                <span class="text text-h3" :class="{'disabled': isCategoryIncludeInHero || recipeDataForCategories.length > 0 || categoryPromotedRecipes.length > 0}">
                  {{ $t('CATEGORY.DELETE_CATEGORY') }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div v-if="finalAvailableLangs && finalAvailableLangs.length > 1" class="category-variant-section">
          <div class="category-variants-main">
            <div class="category-variants text-h5 font-normal">
              Category Variants:
              <div v-show="isCategoryAlertIcon" class="tag-variant-tooltip-section">
                <div class="tooltip-main-container-for-tag-variant simple-data-tooltip"
                  :data-tooltip-text="langVariantTooltip"
                >
                  <img alt="alert" class="alert-image" src="@/assets/images/red-info.svg?skipsvgo=true" >
                </div>
              </div>
            </div>
            <div
              class="add-variant-section"
              :class="{
                'simple-data-tooltip': recipeVariantLanguageList.length < 1,
              }"
              :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
            >
              <button
                  type="button"
                  class="btn-green-text btn-small"
                  :disabled="recipeVariantLanguageList.length < 1"
                  @click="openRecipeVariantPopUp()"
                >
                  <img alt="download" src="@/assets/images/category-add.png" />
                  <span>{{ $t("BUTTONS.ADD_VARIANT") }}</span>
                </button>
            </div>
          </div>
          <div class="add-category-variant color-gray" v-if="recipeVariantList.length <= 0">
            Add category variants to support multiple languages.
          </div>
          <div class="category-variant-card-main" v-else>
            <template v-for="(categoryVariant, index) in recipeVariantList" :key="categoryVariant.lang">
              <variant-card-field
                v-if="categoryVariant?.lang !== lang"
                v-model="categoryVariant.name"
                :prefix-label="displayLanguageCode(categoryVariant.lang)"
                input-placeholder="Enter name"
                :is-delete-action-disabled="isDeleteVariantVisible(categoryVariant)"
                delete-action-tooltip-text="Cannot delete because category variant is used by recipes or category groups."
                @input-change="inputContentChanged()"
                @delete-action="deleteCategoryVariant(categoryVariant, index)"
              ></variant-card-field>
            </template>
          </div>
        </div>
    </page-top-block>
    <simple-content-wrapper
      class="add-recipes-section"
      v-if="!props.isEdit && recipeDataForCategories.length === 0 && categoryPromotedRecipes.length === 0"
    >
      <div class="add-recipes-content">
        <div class="add-recipes-left">
          <div class="add-recipes-header">
            <h2 class="add-recipes-title">Add Recipes to Category</h2>
            <p class="add-recipes-subtitle">Add a group of recipes to your new category</p>
          </div>
          <div class="add-recipes-actions">
            <button
              type="button"
              class="btn-green add-recipes-btn"
              @click="addRecipeToCategory"
            >
              Add Recipes
            </button>
          </div>
        </div>
        <div class="add-recipes-right">
          <div class="add-recipes-illustration">
            <img
              alt="Pan"
              class="pan-image"
              src="@/assets/images/pan-image.png"
            />
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <simple-content-wrapper
      class="category-recipes-combined-wrapper"
      v-if="props.isEdit || (!props.isEdit && (recipeDataForCategories.length > 0 || categoryPromotedRecipes.length > 0))"
    >
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ categoryPromotedRecipesTotal }} Promoted Recipe{{ categoryPromotedRecipesTotal !== 1 ? 's' : '' }}
                </span>
                <div class="text-title-2 font-normal">
                  <span class="color-grey">Promoted recipes are the first to be displayed in category</span>
                </div>
              </div>
            </div>

            <div class="recipe-table-content">
              <div
                class="add-zero-section"
                v-if="(!categoryPromotedRecipes || categoryPromotedRecipes.length === 0) && !isPageLoading && !isUpdating"
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray">
                    0 PROMOTED RECIPES.
                  </span>
                  <span class="normal text-title-2 font-normal">
                    Recipes will be auto-selected.
                  </span>
                </div>
              </div>

              <simple-table
                v-if="categoryPromotedRecipes.length > 0"
                :column-names="promotedRecipeColumnNames"
                :column-keys="promotedRecipeColumnKeys"
                :data-source="categoryPromotedRecipes"
                table-class="promoted-recipe-table"
              >
                <template v-slot:image="props">
                  <img
                    :src="props.data?.media?.image || defaultImage"
                    :alt="props.data?.title"
                    class="recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="recipe-isin">{{ props.data?.isin }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="recipe-title">{{ props.data?.title }}</span>
                </template>

                <template v-slot:totalTime="props">
                  <span class="recipe-time">{{ getRecipeTime(props.data) }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="recipe-ingredients">{{ getIngredientCount(props.data) }} ingredients</span>
                </template>

                <template v-slot:actions="props">
                  <div class="recipe-actions">
                    <body-menu
                      :actions="getPromotedRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>
            </div>
          </div>
        </div>
      </div>

      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ recipeForCategoriesTotal }} Recipe{{ recipeForCategoriesTotal !== 1 ? 's' : '' }} in Category
                </span>
              </div>
              <div class="recipe-header-actions" v-if="!isSelectionEnabled">
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="selectRecipes"
                  v-if="recipeDataForCategories.length > 0"
                >
                  Select
                </button>
                <search-bar :isInForm="true" customPlaceholder="Search for recipe name"/>
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="addRecipeToCategory()"
                >
                  <img alt="download" src="@/assets/images/category-add.png?skipsvgo=true" />
                  <span>{{ $t("BUTTONS.ADD_RECIPE") }}</span>
                </button>
              </div>
            </div>

            <simple-sticky-wrapper
              v-show="isSelectionEnabled"
              :top="70"
              :distance="400"
              class="edit-category-selection-container"
            >
              <div class="edit-category-selection-panel">
                <div id="selectAllCheckboxId" class="edit-category-select-all-checbox-section">
                  <label class="edit-category-checkbox-section">
                    <input type="checkbox" :checked="selectionOfRecipes[0].isSelected" @click="selectAllMatches()" >
                    <span class="checkmark"></span>
                  </label>
                </div>
                <div @click="selectAllMatches()" class="edit-category-select-all-text text-h3">Select All</div>
                <div class="edit-category-selection">
                  <div class="edit-category-selected-text">
                    {{ checkSelectedRecipes }} selected
                    <span v-if="checkSelectedRecipes > 0" class="edit-category-selected-cross-icon">
                      <img src="@/assets/images/close.svg?skipsvgo=true" @click="removeAllSelected()" alt="edit-category-close-icon">
                    </span>
                  </div>
                </div>
                <div class="edit-category-btn-container">
                  <button
                    type="button"
                    :class="checkSelectedRecipes == 0 ? 'disabled-button btn-red' : 'btn-red'"
                    @click="deleteSelect()"
                  >
                    {{ $t('BUTTONS.REMOVE_BUTTON') }}
                  </button>
                </div>
                <button type="button" class="edit-category-cancel-btn text-h3" @click="cancelSelect()">
                  {{ $t('BUTTONS.CANCEL_BUTTON') }}
                </button>
              </div>
            </simple-sticky-wrapper>

            <div class="recipe-table-content">
              <div
                class="add-zero-section category-recipe-section"
                v-if="recipeDataForCategories.length === 0 && !isPageLoading"
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray">0 RECIPES IN CATEGORY.</span>
                  <span class="normal text-title-2 font-normal">Add recipes to this category.</span>
                </div>
              </div>

              <simple-table
                v-if="recipeDataForCategories.length > 0"
                :column-names="categoryRecipeColumnNames"
                :column-keys="categoryRecipeColumnKeys"
                :data-source="recipeDataForCategories"
                table-class="category-recipe-table"
                :row-class="(data, index) => isSelectionEnabled && data.isSelectedToDelete ? 'recipe-selected-color-category' : ''"
                :row-click="isSelectionEnabled ? (data, index) => selectMatchToDelete(index, data) : null"
                :body-style="{ cursor: isSelectionEnabled ? 'pointer' : 'default' }"
              >
                <template v-slot:checkbox="props" v-if="isSelectionEnabled">
                  <div class="edit-category-product-table-srno-checkbox">
                    <div class="edit-category-select-all-checbox-section">
                      <label class="edit-category-checkbox-section">
                        <input
                          @click.stop="handleCheckboxClick(null, props.data)"
                          :checked="props.data.isSelectedToDelete || false"
                          type="checkbox"
                        >
                        <span class="checkmark"></span>
                      </label>
                    </div>
                  </div>
                </template>

                <template v-slot:image="props">
                  <img
                    :src="getRecipeImage(props.data)"
                    :alt="props.data?.title"
                    class="recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="recipe-isin">{{ props.data?.isin }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="recipe-title">{{ props.data?.title }}</span>
                </template>

                <template v-slot:totalTime="props">
                  <span class="recipe-time">{{ getRecipeTime(props.data) }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="recipe-ingredients">{{ getIngredientCount(props.data) }} ingredients</span>
                </template>

                <template v-slot:actions="props">
                  <div class="recipe-actions" v-if="!isSelectionEnabled">
                    <button
                      type="button"
                      class="btn-green-outline"
                      @click="promoteRecipe(props.data)"
                    >
                      Promote
                    </button>
                    <body-menu
                      :actions="getCategoryRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>

              <pagination
                v-if="recipeForCategoriesTotal > sizeRecipe && recipeDataForCategories.length > 0"
                :list="recipeDataForCategories"
                :list-total="recipeForCategoriesTotal"
                :size-per-page="sizeRecipe"
                :current-page="Math.floor(fromRecipe / sizeRecipe) + 1"
                @page-change="handleRecipesPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <select-the-language-modal
      v-if="hasRecipeVariantLanguagePopUp"
      :closeModal="() => hasRecipeVariantLanguagePopUp = false"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
      @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
      @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
      :recipeVariantLanguageList="recipeVariantLanguageList"
      :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
    />

    <add-variant
      v-if="isAddVariantCategoryNamePopUp"
      :closeModal="() => isAddVariantCategoryNamePopUp = false"
      :typeName="'Category'"
      :addVariantSelectedLanguage="recipeVariantLanguage"
      :itemName="categoriesName"
      @addConfirmVariant="addRecipeVariant"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @backToRoute="backToSelectLanguageVariantPopUp"
    />
  </content-wrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import { useStore } from 'vuex';
import { useNuxtApp } from '#app';
import ContentWrapper from '@/components/content-wrapper/content-wrapper.vue';
import PageTopBlock from '@/components/page-top-block.vue';
import SimpleContentWrapper from '@/components/simple-content-wrapper.vue';
import SimpleTable from '@/components/simple-table/simple-table.vue';
import SimpleStickyWrapper from '@/components/simple-sticky-wrapper.vue';
import SearchBar from '@/components/search-bar/search-bar.vue';
import Pagination from '@/components/pagination.vue';
import ImageBox from '@/components/image-box.vue';
import NameField from '@/components/name-field.vue';
import ButtonWithSwitcher from '@/components/button-with-switcher.vue';
import SaveModal from '@/components/save-modal.vue';
import CancelModal from '@/components/cancel-modal.vue';
import SavingModal from '@/components/saving-modal.vue';
import DeleteModal from '@/components/delete-modal.vue';
import AddRecipeModal from '@/components/add-recipe-modal.vue';
import BodyMenu from '@/components/body-menu.vue';
import RecipePreviewDetailModal from '@/components/pages/recipes/recipe-preview-detail-modal.vue';
import VariantCardField from '@/components/variant-card-field/variant-card-field.vue';
import SelectTheLanguageModal from '@/components/select-the-language.vue';
import AddVariant from '@/components/add-variant.vue';
import { useBaseModal } from '@/composables/useBaseModal';
import { useProjectLang } from '@/composables/useProjectLang';
import { useTimeUtils } from '@/composables/useTimeUtils';
import { useQueryUtils } from '@/composables/useQueryUtils';
import defaultImage from '@/assets/images/default_recipe_image.png';
import savePopupImage from '@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png';
import publishVariantIcon from '@/assets/images/publish-variant-icon.png';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const store = useStore();
const { $t } = useNuxtApp();
const { readyProject } = useProjectLang();
const { parseDurationString } = useTimeUtils();
const { getSearchQuery } = useQueryUtils();

const { openModal, closeModal } = useBaseModal({
  CategorySaveModal: SaveModal,
  CategoryCancelModal: CancelModal,
  CategorySavingModal: SavingModal,
  CategoryDeleteModal: DeleteModal,
  CategorySelectDeleteModal: DeleteModal,
  CategoryAddRecipeModal: AddRecipeModal,
  CategoryRecipePreviewModal: {
    component: RecipePreviewDetailModal,
    hideCloseBtn: false,
    modalWrapperClass: "recipe-preview-detail-modal-wrapper",
    skipClickOutside: true,
  },
});

const categoriesName = ref('');
const categoriesSlug = ref('');
const image = ref("");
const isPublish = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const hasSlugExist = ref(false);
const hasChanges = ref(false);
const categoryISIN = ref('');

const isCategoriesStatus = ref("hidden");
const categoriesState = ref("published");
const isCategoryIncludeInHero = ref(false);

const recipeDataForCategories = ref([]);
const categoryPromotedRecipes = ref([]);
const searchConfig = ref({});
const recipeForCategoriesTotal = ref(0);
const categoryPromotedRecipesTotal = ref(0);
const fromRecipe = ref(0);
const sizeRecipe = ref(10);
const fromPromotedRecipe = ref(0);
const sizePromotedRecipe = ref(10);
const isPageLoading = ref(false);
const isUpdating = ref(false);

const searchcopy = ref('');
const totalPromotedRemovedIsin = ref([]);
const addedIsins = ref([]);
const lang = computed(() => store.getters['userData/getDefaultLang']);

const selectedCategoryRecipe = ref([]);
const recipesAfterPageChange = ref([]);
const recipeMatchesIsinsTagRemove = ref([]);

const isSelectionEnabled = ref(false);
const selectedProducts = ref([]);
const selectionOfRecipes = ref([
  {
    isSelected: false,
  },
]);
const recipeMatchesIsinsRemove = ref([]);
const removeRecipeList = ref([]);

const recipeVariantList = ref([]);
const initiallyVariantSupported = ref([]);
const saveRemovedCategoryVariants = ref([]);
const recipeVariantLanguageList = ref([]);
const recipeVariantLanguage = ref('');
const recipeVariantLanguageIndex = ref(0);
const variantName = ref('');
const hasRecipeVariantLanguagePopUp = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const hasDisableSelectLanguageButton = ref(false);
const isAddVariantCategoryNamePopUp = ref(false);
const categoryAssociations = ref({});
const finalAvailableLangs = ref([]);
const isCategoryAlertIcon = ref(false);
const langVariantTooltip = ref('');
const selectedDefaultLang = ref([]);

const RECIPE_ACTION_CASE = {
  PREVIEW: 'preview',
  REMOVE: 'remove',
  PROMOTE: 'promote',
  UNPROMOTE: 'unpromote',
};

const promotedRecipeColumnNames = ['', 'Recipe ISIN', 'Recipe Title', 'Total Time', 'Ingredient Count', ''];
const promotedRecipeColumnKeys = ['image', 'isin', 'title', 'totalTime', 'ingredientCount', 'actions'];

const categoryRecipeColumnNames = computed(() => {
  if (isSelectionEnabled.value) {
    return ['', '', 'Recipe ISIN', 'Recipe Title', 'Total Time', 'Ingredient Count', ''];
  }
  return ['', 'Recipe ISIN', 'Recipe Title', 'Total Time', 'Ingredient Count', ''];
});

const categoryRecipeColumnKeys = computed(() => {
  if (isSelectionEnabled.value) {
    return ['checkbox', 'image', 'isin', 'title', 'totalTime', 'ingredientCount', 'actions'];
  }
  return ['image', 'isin', 'title', 'totalTime', 'ingredientCount', 'actions'];
});

const hintID = computed(() => {
  return props.isEdit && categoryISIN.value ? `ISIN: ${categoryISIN.value}` : '';
});

const isCategoryValid = computed(() => {
  return categoriesName.value.trim() !== '' && image.value !== defaultImage;
});

const selectedRecipeCount = ref(0);

const checkSelectedRecipes = selectedRecipeCount;

const updateSelectionCount = () => {
  const count = recipeDataForCategories.value.filter(data => data.isSelectedToDelete === true).length;
  selectedRecipeCount.value = count;
  return count;
};

const uploadFile = (file) => {
  if (file) {
    image.value = URL.createObjectURL(file);
    hasChanges.value = true;
    uploadImagePercentage.value = 100;
    loadedImageSize.value = file.size;
  }
};

const handleSlugInput = () => {
  hasChanges.value = true;
};

const publishCategoryAction = () => {
  isPublish.value = !isPublish.value;
  hasChanges.value = true;
};

const publishToggleBtnAsync = async () => {
  if (categoriesState.value !== "published") {
    return;
  }

  if (isCategoriesStatus.value === "active") {
    isCategoriesStatus.value = "hidden";
    hasChanges.value = true;
  } else {
    isCategoriesStatus.value = "active";
    hasChanges.value = true;
  }
};

const publishToggleBtnPopup = () => {
  if (categoriesState.value !== "published") {
  } else {
    publishToggleBtnAsync();
  }
};

const deleteCategory = () => {
  if (!props.isEdit || !categoryISIN.value) return;

  openModal({
    name: 'CategoryDeleteModal',
    props: {
      closeModal: () => closeModal('CategoryDeleteModal'),
      productInfoTitle: 'Delete Category?',
      productDescriptionOne: 'Do you want to delete this',
      productDescriptionTwo: 'category?',
      deleteItem: deleteCategoryConfirm,
      availableLanguage: 0,
      buttonText: 'Delete',
    },
  });
};

const deleteCategoryConfirm = async () => {
  try {
    closeModal('CategoryDeleteModal');

    openModal({
      name: 'CategorySavingModal',
      props: {
        status: 'saving',
      },
    });
    await store.dispatch('categories/deleteCategoryAsync', {
      isin: categoryISIN.value,
    });

    closeModal('CategorySavingModal');

    hasChanges.value = false;

    setTimeout(() => {
      router.push('/category');
    }, 100);

  } catch (error) {
    console.error('[IQ][CategoryForm] Error deleting category:', error);
    closeModal('CategorySavingModal');
  }
};

const openPreviewSaveModal = () => {
  if (!isCategoryValid.value) return;

  const isPublishing = isPublish.value;
  const buttonName = isPublishing ? 'Publish' : 'Save';
  const description = isPublishing
    ? 'Do you want to publish your Category form?'
    : 'Do you want to save as draft your Category form?';
  const imageName = isPublishing ? publishVariantIcon : savePopupImage;

  openModal({
    name: 'CategorySaveModal',
    props: {
      closeModal: () => closeModal('CategorySaveModal'),
      saveAndPublishFunction: saveButtonClickAsync,
      availableLang: [],
      buttonName,
      description,
      imageName,
      slugCheckConfirm: false,
      hasSlugExist: hasSlugExist.value,
    },
  });
};

const saveButtonClickAsync = async () => {
  try {
    closeModal('CategorySaveModal');

    openModal({
      name: 'CategorySavingModal',
      props: {
        status: isPublish.value ? 'publishing' : 'saving',
      },
    });

    if (props.isEdit && categoryISIN.value) {
      await updateCategoryWithVariants();
    } else {
      await createCategoryWithVariants();
    }

    closeModal('CategorySavingModal');

    hasChanges.value = false;

    setTimeout(() => {
      router.push('/category');
    }, 100);

  } catch (error) {
    console.error('[IQ][CategoryForm] Error saving category:', error);
    closeModal('CategorySavingModal');
  }
};

const createCategoryWithVariants = async () => {
  const currentLang = store.getters['userData/getDefaultLang'];

  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: currentLang,
  };

  const allVariants = [defaultVariantData, ...recipeVariantList.value];

  const payload = {
    type: "category",
    data: updatedRecipeVariantList(allVariants),
    image: image.value !== defaultImage ? { [currentLang]: image.value } : undefined,
    slug: categoriesSlug.value ? { [currentLang]: categoriesSlug.value } : undefined,
  };

  if (!payload.image) delete payload.image;
  if (!payload.slug) delete payload.slug;

  await store.dispatch('categories/postCategoryOrCategoryGroupAsync', {
    payload,
    lang: currentLang
  });
};

const updateCategoryWithVariants = async () => {
  const currentLang = store.getters['userData/getDefaultLang'];

  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: currentLang,
  };

  const allVariants = [defaultVariantData, ...recipeVariantList.value];

  const payload = {
    isin: categoryISIN.value,
    type: "category",
    data: updatedRecipeVariantList(allVariants),
    image: image.value !== defaultImage ? { [currentLang]: image.value } : undefined,
    slug: categoriesSlug.value ? { [currentLang]: categoriesSlug.value } : undefined,
  };

  if (!payload.image) delete payload.image;
  if (!payload.slug) delete payload.slug;

  await store.dispatch('categories/postCategoryOrCategoryGroupAsync', {
    payload,
    lang: currentLang
  });

  if (saveRemovedCategoryVariants.value?.length) {
    await deleteVariantAsync();
  }
};

const updatedRecipeVariantList = (variantList) => {
  const updatedVariantList = {};

  variantList.forEach((variant) => {
    if (variant.name && variant.lang) {
      updatedVariantList[variant.lang] = {
        name: variant.name.trim()
      };
    }
  });

  return updatedVariantList;
};

const addRecipeToCategory = () => {
  openModal({
    name: 'CategoryAddRecipeModal',
    props: {
      closeModal: () => handleAddRecipeModalClose(),
      recipeDataForCategories: recipeDataForCategories.value || [],
      categoryPromotedRecipes: categoryPromotedRecipes.value || [],
      selectedCategoryRecipe: selectedCategoryRecipe.value || [],
      recipesAfterPageChange: recipesAfterPageChange.value || [],
      recipeMatchesIsinsTagRemove: recipeMatchesIsinsTagRemove.value || [],
      removeRecipeList: [],
      removeRecipeTagList: [],
      isEditCategories: props.isEdit,
      isAddCategory: !props.isEdit,
      isPageLoading: false,
      isAddTag: false,
      isEditTag: false,
      preventEnterAndSpaceKeyPress: (event) => {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
        }
      },
      campaignModifiedAddRecipe: () => {
        hasChanges.value = true;
      },
      onRecipeAdded: (addedRecipes) => {
        handleRecipeAdded(addedRecipes);
      }
    },
  });
};

const handleAddRecipeModalClose = async () => {
  closeModal('CategoryAddRecipeModal');
  if (props.isEdit && categoryISIN.value) {
    const langValue = store.getters['userData/getDefaultLang'];
    await Promise.all([
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue),
      getPromotedRecipesForCategoriesAsync(categoryISIN.value, langValue)
    ]);
  }
};

const handleRecipeAdded = async (addedRecipes) => {
  if (addedRecipes && addedRecipes.length > 0) {
    try {
      const addedRecipeIsins = addedRecipes.map(recipe => recipe.isin);

      if (props.isEdit && categoryISIN.value) {
        await postCategoryRecipeAsync(addedRecipeIsins);

        const langValue = store.getters['userData/getDefaultLang'];
        await Promise.all([
          getRecipeDataForCategoriesAsync(categoryISIN.value, langValue),
          getPromotedRecipesForCategoriesAsync(categoryISIN.value, langValue)
        ]);
        return;
      }

      addedRecipes.forEach(recipe => {
        const existsInRegular = recipeDataForCategories.value.find(r => r.isin === recipe.isin);
        const existsInPromoted = categoryPromotedRecipes.value.find(r => r.isin === recipe.isin);

        if (!existsInRegular && !existsInPromoted) {
          const formattedRecipe = {
            isin: recipe.isin,
            title: recipe.title?.[lang.value] || recipe.title || '',
            totalTime: recipe.time?.total || '',
            ingredientCount: recipe.ingredients?.[lang.value]?.length || 0,
            image: recipe.media?.[lang.value]?.image || recipe.media?.[lang.value]?.externalImageUrl || defaultImage,
            media: recipe.media,
            time: recipe.time,
            ingredients: recipe.ingredients,
            isPromoted: false
          };

          recipeDataForCategories.value.unshift(formattedRecipe);

          if (!selectedCategoryRecipe.value.includes(recipe.isin)) {
            selectedCategoryRecipe.value.push(recipe.isin);
          }

          if (!addedIsins.value.includes(recipe.isin)) {
            addedIsins.value.push(recipe.isin);
          }
        }
      });

      hasChanges.value = true;

      recipeForCategoriesTotal.value = recipeDataForCategories.value.length;

    } catch (error) {
      console.error('[IQ][CategoryForm] Error adding recipes to category:', error);
    }
  }
};

const postCategoryRecipeAsync = async (recipeIsins) => {
  try {
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "add",
        isin: categoryISIN.value,
        targets: recipeIsins,
      },
    };

    const response = await store.dispatch("categories/postCategoryRecipeAsync", { payload });

    if (response?.opId) {
      await checkOperationStatusAsync(response.opId);
    }

    return response;
  } catch (error) {
    console.error('[IQ][CategoryForm] Error in postCategoryRecipeAsync:', error);
    throw error;
  }
};

const selectRecipes = () => {
  isSelectionEnabled.value = true;
  recipeDataForCategories.value.forEach((recipe) => {
    if (recipe.isSelectedToDelete === undefined) {
      recipe.isSelectedToDelete = false;
    }
  });
};

const selectAllMatches = () => {
  selectionOfRecipes.value[0].isSelected = !selectionOfRecipes.value[0].isSelected;
  const updatedRecipes = recipeDataForCategories.value.map((item) => ({
    ...item,
    isSelectedToDelete: selectionOfRecipes.value[0].isSelected
  }));

  recipeDataForCategories.value = updatedRecipes;

  if (selectionOfRecipes.value[0].isSelected) {
    selectedProducts.value = [...updatedRecipes];
  } else {
    selectedProducts.value = [];
  }

  updateSelectionCount();
};

const handleCheckboxClick = async (index, data) => {

  if (isSelectionEnabled.value && data) {
    let actualIndex = index;
    if (actualIndex === undefined || actualIndex === null) {
      actualIndex = recipeDataForCategories.value.findIndex(item => item.isin === data.isin);
    }

    if (actualIndex >= 0 && actualIndex < recipeDataForCategories.value.length) {
      const item = recipeDataForCategories.value[actualIndex];

      const wasSelected = item.isSelectedToDelete || false;
      item.isSelectedToDelete = !wasSelected;

      recipeDataForCategories.value = [...recipeDataForCategories.value];

      if (!wasSelected) {
        const exists = selectedProducts.value.find(p => p.isin === item.isin);
        if (!exists) {
          selectedProducts.value.push(item);
        }
      } else {
        selectedProducts.value = selectedProducts.value.filter((insideItem) => insideItem.isin !== item.isin);
      }

      await nextTick();
      updateSelectionCount();

      await nextTick();

      checkSelected();
    } else {
      console.error('[IQ][CategoryForm] Could not find item with ISIN:', data.isin, 'in recipeDataForCategories');
    }
  }
};

const selectMatchToDelete = (data, product) => {
  if (isSelectionEnabled.value) {
    const item = recipeDataForCategories.value[data];
    if (item) {
      const wasSelected = item.isSelectedToDelete || false;

      const updatedRecipes = [...recipeDataForCategories.value];
      updatedRecipes[data] = { ...item, isSelectedToDelete: !wasSelected };
      recipeDataForCategories.value = updatedRecipes;

      if (!wasSelected) {
        const exists = selectedProducts.value.find(p => p.isin === item.isin);
        if (!exists) {
          selectedProducts.value.push(updatedRecipes[data]);
        }
      } else {
        selectedProducts.value = selectedProducts.value.filter((insideItem) => insideItem.isin !== item.isin);
      }

      updateSelectionCount();

      checkSelected();
    }
  }
};

const checkSelected = () => {
  let count = 0;
  recipeDataForCategories.value.forEach((item) => {
    if (item.isSelectedToDelete) {
      count += 1;
    }
  });
  selectionOfRecipes.value[0].isSelected = count === recipeDataForCategories.value.length;
};

const cancelSelect = () => {
  isSelectionEnabled.value = false;
  selectedProducts.value = [];
  selectionOfRecipes.value[0].isSelected = false;

  if (recipeDataForCategories.value.length > 0) {
    const updatedRecipes = recipeDataForCategories.value.map((item) => ({
      ...item,
      isSelectedToDelete: false
    }));
    recipeDataForCategories.value = updatedRecipes;
  }

  updateSelectionCount();
};

const removeAllSelected = () => {
  const updatedRecipes = recipeDataForCategories.value.map((item) => ({
    ...item,
    isSelectedToDelete: false
  }));
  recipeDataForCategories.value = updatedRecipes;

  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];

  updateSelectionCount();
};

const deleteSelect = () => {
  if (checkSelectedRecipes.value > 0) {
    openModal({
      name: 'CategorySelectDeleteModal',
      props: {
        closeModal: () => closeModal('CategorySelectDeleteModal'),
        productInfoTitle: 'Remove Recipe?',
        productDescriptionOne: 'Are you sure you want to remove this recipe from the',
        productDescriptionTwo: 'category?',
        deleteItem: deleteSelectProductMatches,
        availableLanguage: 0,
        buttonText: 'Remove',
      },
    });
  } else {
    console.warn('[IQ][CategoryForm] No recipes selected for deletion');
  }
};

const deleteSelectProductMatches = () => {
  try {
    const selectedIsinsToRemove = [];

    recipeDataForCategories.value.forEach((recipe) => {
      if (recipe.isSelectedToDelete) {
        selectedIsinsToRemove.push(recipe.isin);
        removeRecipeList.value.push(recipe);
      }
    });

    if (selectedIsinsToRemove.length === 0) {
      console.warn('[IQ][CategoryForm] No recipes selected for removal');
      closeModal('CategorySelectDeleteModal');
      return;
    }

    recipesAfterPageChange.value = recipesAfterPageChange.value.filter((data) => {
      return !selectedIsinsToRemove.includes(data.isin);
    });
    const originalCount = recipeDataForCategories.value.length;
    recipeDataForCategories.value = recipeDataForCategories.value.filter((data) => {
      return !selectedIsinsToRemove.includes(data.isin);
    });

    recipeMatchesIsinsRemove.value.push(...selectedIsinsToRemove);

    hasChanges.value = true;

    closeModal('CategorySelectDeleteModal');
    selectedProducts.value = [];
    selectionOfRecipes.value[0].isSelected = false;
    isSelectionEnabled.value = false;

    recipeForCategoriesTotal.value = recipeDataForCategories.value.length;

  } catch (error) {
    console.error('[IQ][CategoryForm] Error removing selected recipes:', error);
    closeModal('CategorySelectDeleteModal');
  }
};

watch(() => getSearchQuery(), (newSearchQuery, oldSearchQuery) => {
  if (newSearchQuery !== oldSearchQuery) {

    searchcopy.value = newSearchQuery || '';

    if (props.isEdit && categoryISIN.value) {
      const langValue = store.getters['userData/getDefaultLang'];
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    }
  }
}, { immediate: false });

const getRecipeImage = (recipe) => {
  if (!recipe) return defaultImage;
  if (recipe.image) {
    return recipe.image;
  }

  if (recipe.media) {
    const currentLang = store.getters['userData/getDefaultLang'] || 'fr-FR';

    if (recipe.media[currentLang]?.image) {
      return recipe.media[currentLang].image;
    }

    if (recipe.media[currentLang]?.thumbnailImageUrl) {
      return recipe.media[currentLang].thumbnailImageUrl;
    }

    if (recipe.media[currentLang]?.imageList?.length > 0) {
      return recipe.media[currentLang].imageList[0].url;
    }

    const availableLanguages = Object.keys(recipe.media);
    for (const lang of availableLanguages) {
      if (recipe.media[lang]?.image) {
        return recipe.media[lang].image;
      }
      if (recipe.media[lang]?.thumbnailImageUrl) {
        return recipe.media[lang].thumbnailImageUrl;
      }
      if (recipe.media[lang]?.imageList?.length > 0) {
        return recipe.media[lang].imageList[0].url;
      }
    }
  }

  return defaultImage;
};

const getRecipeTime = (recipe) => {
  if (!recipe) return '';

  if (recipe.totalTime && typeof recipe.totalTime === 'string') {
    return recipe.totalTime;
  }

  if (recipe.time?.total) {
    return parseDurationString(recipe.time.total);
  }

  if (recipe.time) {
    const { cook, prep } = recipe.time;
    if (cook && prep) {
      const cookTime = parseDurationString(cook);
      const prepTime = parseDurationString(prep);
      if (cookTime && prepTime) {
        return `${prepTime} prep + ${cookTime} cook`;
      }
    }

    if (cook) {
      return parseDurationString(cook);
    }

    if (prep) {
      return parseDurationString(prep);
    }
  }

  return '';
};

const getIngredientCount = (recipe) => {
  if (!recipe) return 0;

  const currentLang = store.getters['userData/getDefaultLang'] || 'fr-FR';

  if (recipe.ingredients && typeof recipe.ingredients === 'object') {
    if (recipe.ingredients[currentLang] && Array.isArray(recipe.ingredients[currentLang])) {
      return recipe.ingredients[currentLang].length;
    }

    if (Array.isArray(recipe.ingredients)) {
      return recipe.ingredients.length;
    }

    const availableLanguages = Object.keys(recipe.ingredients);
    for (const lang of availableLanguages) {
      if (Array.isArray(recipe.ingredients[lang])) {
        return recipe.ingredients[lang].length;
      }
    }
  }

  return 0;
};

const getPromotedRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: 'Preview',
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.UNPROMOTE, recipe.isin],
      label: 'Unpromote',
    },
  ];
};

const getCategoryRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: 'Preview',
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.REMOVE, recipe.isin],
      label: 'Remove',
    },
  ];
};

const handleRecipeAction = ([actionType, recipeIsin]) => {
  const recipe = [...categoryPromotedRecipes.value, ...recipeDataForCategories.value]
    .find(r => r.isin === recipeIsin);

  if (!recipe) {
    console.error('[IQ][CategoryForm] Recipe not found for action:', actionType, recipeIsin);
    return;
  }

  switch (actionType) {
    case RECIPE_ACTION_CASE.PREVIEW:
      openModal({
        name: 'CategoryRecipePreviewModal',
        props: {
          recipeIsin: recipeIsin,
          checkRecipePreviewVideo: () => {},
        },
      });
      break;
    case RECIPE_ACTION_CASE.PROMOTE:
      promoteRecipe(recipe);
      break;
    case RECIPE_ACTION_CASE.UNPROMOTE:
      removePromotedRecipe(recipe);
      break;
    case RECIPE_ACTION_CASE.REMOVE:
      removeRecipeFromCategory(recipe);
      break;
    default:
      console.warn('[IQ][CategoryForm] Unknown action type:', actionType);
  }
};

const handleRecipesPageChange = (page) => {
  fromRecipe.value = (page - 1) * sizeRecipe.value;
  if (props.isEdit && categoryISIN.value) {
    const langValue = store.getters['userData/getDefaultLang'];
    getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
  }
};

const removeRecipeFromCategory = (recipe) => {
  const index = recipeDataForCategories.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    recipeDataForCategories.value.splice(index, 1);

    const isinIndex = addedIsins.value.indexOf(recipe.isin);
    if (isinIndex > -1) {
      addedIsins.value.splice(isinIndex, 1);
    }

    hasChanges.value = true;
  }
};

const removePromotedRecipe = (recipe) => {
  const index = categoryPromotedRecipes.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    const unpromotedRecipe = { ...recipe, isPromoted: false };
    categoryPromotedRecipes.value.splice(index, 1);

    recipeDataForCategories.value.unshift(unpromotedRecipe);

    categoryPromotedRecipesTotal.value -= 1;
    recipeForCategoriesTotal.value += 1;

    if (!totalPromotedRemovedIsin.value.includes(recipe.isin)) {
      totalPromotedRemovedIsin.value.push(recipe.isin);
    }

    hasChanges.value = true;
  }
};

const promoteRecipe = (recipe) => {
  const index = recipeDataForCategories.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    const promotedRecipe = { ...recipe, isPromoted: true };
    recipeDataForCategories.value.splice(index, 1);
    categoryPromotedRecipes.value.push(promotedRecipe);

    categoryPromotedRecipesTotal.value += 1;
    recipeForCategoriesTotal.value -= 1;

    const removedIndex = totalPromotedRemovedIsin.value.indexOf(recipe.isin);
    if (removedIndex > -1) {
      totalPromotedRemovedIsin.value.splice(removedIndex, 1);
    }

    hasChanges.value = true;

    if (props.isEdit && categoryISIN.value) {
      const langValue = store.getters['userData/getDefaultLang'];
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    }
  }
};

const getPromotedRecipesForCategoriesAsync = async (isin, langParam) => {
  try {
    if (!isin) {
      console.error('[IQ][CategoryForm] Missing required parameter: isin');
      return;
    }

    const lang = langParam || store.getters['userData/getDefaultLang'];

    await store.dispatch('categories/getPromotedRecipesForCategoriesAsync', {
      isin,
      lang
    });

    const response = store.getters['categories/getPromotedRecipesForCategories'];
    if (response) {
      if (Array.isArray(response)) {
        categoryPromotedRecipes.value = response.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.length;
      } else if (response.promotedRecipes && Array.isArray(response.promotedRecipes)) {
        categoryPromotedRecipes.value = response.promotedRecipes.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.promotedRecipes.length;
      } else if (response.results && Array.isArray(response.results)) {
        categoryPromotedRecipes.value = response.results.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.results.length;
      } else if (response.data && Array.isArray(response.data)) {
        categoryPromotedRecipes.value = response.data.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.data.length;
      } else {
        console.warn('[IQ][CategoryForm] Unexpected promoted recipes response format:', response);
        categoryPromotedRecipes.value = [];
        categoryPromotedRecipesTotal.value = 0;
      }
    } else {
      categoryPromotedRecipes.value = [];
      categoryPromotedRecipesTotal.value = 0;
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading promoted recipes:', error);
    categoryPromotedRecipes.value = [];
  }
};

const getRecipeDataForCategoriesAsync = async (isin, langParam) => {
  try {
    if (!isin) {
      console.error('[IQ][CategoryForm] Missing required parameter: isin');
      return;
    }

    const lang = langParam || store.getters['userData/getDefaultLang'];

    const promotedRecipeIsins = categoryPromotedRecipes.value.map(recipe => recipe.isin);

    const allExcludingIsins = [
      ...totalPromotedRemovedIsin.value,
      ...promotedRecipeIsins
    ].filter(Boolean);

    const searchQuery = getSearchQuery();
    let finalSearchQuery = '';

    if (searchQuery && searchQuery.trim() !== '') {
      finalSearchQuery = searchQuery.trim();
      searchcopy.value = finalSearchQuery;
    } else if (searchcopy.value && searchcopy.value.trim() !== '') {
      finalSearchQuery = searchcopy.value.trim();
    }

    const payload = {
      country: lang.split('-')[1],
      q: finalSearchQuery,
      excludingIsins: allExcludingIsins.join(','),
      groupsIncludingIsins: addedIsins.value.join(','),
      groups: isin,
      from: fromRecipe.value,
      size: sizeRecipe.value,
      sort: 'lastMod',
    };

    await store.dispatch('categories/getRecipeForCategoriesAsync', { payload });

    const response = store.getters['categories/getRecipeForCategories'];
    if (response?.results && Array.isArray(response.results)) {
      recipeDataForCategories.value = response.results.map(recipe => ({
        ...recipe,
        isPromoted: false
      }));
      recipeForCategoriesTotal.value = response.total || 0;
    } else {
      console.warn('[IQ][CategoryForm] Unexpected recipes response format:', response);
      recipeDataForCategories.value = [];
      recipeForCategoriesTotal.value = 0;
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading recipes for categories:', error);
    recipeDataForCategories.value = [];
    recipeForCategoriesTotal.value = 0;
  }
};

const getSearchConfigAsync = async () => {
  try {
    await store.dispatch('editSearch/getEditSearchAsync');
    const response = store.getters['editSearch/getEditSearch'];
    if (response) {
      searchConfig.value = response;
    } else {
      searchConfig.value = {};
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading search config:', error);
    searchConfig.value = {};
  }
};

const getEditCategoryListAsync = async (isin, lang) => {
  try {
    const response = await fetchCategoryGroupData(isin, lang);

    if (response) {
      processCategoryGroupData(response);
      updateCategoryDetails(response, lang);
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error in getEditCategoryListAsync:', error);
  }
};

const fetchCategoryGroupData = async (isin, lang) => {
  try {
    await store.dispatch("categories/getEditCategoryGroupListAsync", {
      isin,
      lang,
      sectionType: "category",
    });
    return store.getters["categories/getEditCategoryGroupList"];
  } catch (error) {
    console.error('[IQ][CategoryForm] Error in fetchCategoryGroupData:', error);
    return null;
  }
};

const processCategoryGroupData = (response) => {
  const { data } = response;
  selectedDefaultLang.value.forEach((language) => {
    if (data?.[language] && language !== lang.value) {
      const newVariantData = {
        name: data[language].name,
        lang: language,
      };

      recipeVariantList.value.push(newVariantData);
      initiallyVariantSupported.value.push(newVariantData);
    }
  });
};

const updateCategoryDetails = (response, lang) => {
  const { data, isin, status, state, slug } = response;

  categoryISIN.value = isin || "";
  isCategoriesStatus.value = status || "";
  categoriesState.value = state || "";
  categoriesSlug.value = slug?.[lang] || "";

  isPublish.value = status === 'active';

  const langData = data?.[lang];
  if (langData) {
    categoriesName.value = langData.name || "";
    const categoryImage = langData.image || defaultImage;

    if (categoryImage) {
      image.value = categoryImage;
    }
  }

  const categoryData = store.getters['categories/getCategoryData'];
  if (categoryData) {
    if (categoryData.name && !categoriesName.value) {
      categoriesName.value = categoryData.name;
    }
    if (categoryData.slug && !categoriesSlug.value) {
      categoriesSlug.value = categoryData.slug;
    }
    if (categoryData.image && !image.value) {
      image.value = categoryData.image;
    }
  }
};

const openRecipeVariantPopUp = () => {
  hasRecipeVariantLanguagePopUp.value = true;
  hasRecipeVariantLanguageResult.value = false;
  hasDisableSelectLanguageButton.value = false;
  recipeVariantLanguage.value = "";
};

const setRecipeVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  recipeVariantLanguageIndex.value = index;
  hasRecipeVariantLanguageResult.value = false;
  hasDisableSelectLanguageButton.value = true;

  recipeVariantLanguageList.value.forEach((data, idx) => {
    if (data.language === recipeVariantLanguage.value) {
      recipeVariantLanguageList.value.splice(idx, 1);
      recipeVariantLanguageList.value.unshift(data);
    }
  });
};

const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};

const nextCategoryVariantNameModalPopUp = () => {
  hasRecipeVariantLanguagePopUp.value = false;
  isAddVariantCategoryNamePopUp.value = true;
};

const backToSelectLanguageVariantPopUp = () => {
  isAddVariantCategoryNamePopUp.value = false;
  hasRecipeVariantLanguagePopUp.value = true;
};

const addRecipeVariant = (item) => {
  variantName.value = item;
  if (variantName.value !== "") {
    const newVariantData = {
      name: item.trim(),
      lang: recipeVariantLanguage.value,
    };
    recipeVariantList.value.push(newVariantData);
    hasChanges.value = true;
    isAddVariantCategoryNamePopUp.value = false;
    variantName.value = "";

    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter((data) => data.language !== recipeVariantLanguage.value);

    saveRemovedCategoryVariants.value = saveRemovedCategoryVariants.value.filter((data) => data !== recipeVariantLanguage.value);

    getCategoryAssociations();
  }
};

const displayLanguageCode = (item) => {
  if (item) {
    const arr = item.split("-");
    return arr[0].toUpperCase();
  }
  return "";
};

const deleteCategoryVariant = (categoryVariant, index) => {
  if (initiallyVariantSupported.value.some(variant => variant.lang === categoryVariant.lang)) {
    saveRemovedCategoryVariants.value.push(categoryVariant.lang);
  }

  recipeVariantList.value.splice(index, 1);
  hasChanges.value = true;

  const langData = {
    language: categoryVariant.lang,
    language_name: getLanguageName(categoryVariant.lang),
    languageFlag: getLanguageFlag(categoryVariant.lang)
  };

  if (!recipeVariantLanguageList.value.some(lang => lang.language === categoryVariant.lang)) {
    recipeVariantLanguageList.value.push(langData);
  }

  getCategoryAssociations();
};

const isDeleteVariantVisible = (categoryVariant) => {
  return categoryAssociations.value[categoryVariant.lang] > 0;
};

const getCategoryAssociations = async () => {
  if (!props.isEdit || !categoryISIN.value) return;

  const promises = [];
  const variantList = [];

  recipeVariantList.value.forEach((langVariant) => {
    if (langVariant.lang !== lang.value) {
      promises.push(
        store.dispatch('categories/getCategoryAssociationsAsync', {
          isin: categoryISIN.value,
          from: 0,
          size: 15,
          lang: langVariant.lang,
        }).then(() => {
          const response = store.getters['categories/getCategoryAssociations'];
          const object = {
            [langVariant.lang]: response?.recipes?.length || response?.recipeGroups?.length || 0,
          };
          variantList.push(object);
        })
      );
    }
  });

  await Promise.all(promises);
  categoryAssociations.value = Object.assign({}, ...variantList);
};

const getLanguageName = (langCode) => {
  const langMap = {
    'en-US': 'English',
    'fr-FR': 'French',
    'de-DE': 'German',
    'es-ES': 'Spanish',
    'it-IT': 'Italian',
  };
  return langMap[langCode] || langCode;
};

const getLanguageFlag = (langCode) => {
  const flagMap = {
    'en-US': '/assets/images/flags/us.png',
    'fr-FR': '/assets/images/flags/fr.png',
    'de-DE': '/assets/images/flags/de.png',
    'es-ES': '/assets/images/flags/es.png',
    'it-IT': '/assets/images/flags/it.png',
  };
  return flagMap[langCode] || '';
};

const deleteVariantAsync = async () => {
  try {
    await store.dispatch('categories/deleteLanguageVariantAsync', {
      isin: categoryISIN.value,
      lang: saveRemovedCategoryVariants.value,
    });
  } catch (e) {
    console.error('[IQ][CategoryForm] Error deleting variants:', e);
  }
};

const inputContentChanged = () => {
  hasChanges.value = true;
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      const lang = store.getters['userData/getDefaultLang'];

      await initializeAvailableLanguages();

      if (props.isEdit) {
        const isin = route.params.isin;
        categoryISIN.value = isin;

        try {
          await store.dispatch('categories/getCategoryDataAsync', {
            isin,
            lang
          });

          await getEditCategoryListAsync(isin, lang);

          await getPromotedRecipesForCategoriesAsync(isin, lang);

          await getRecipeDataForCategoriesAsync(isin, lang);

          await getSearchConfigAsync();

          await loadCategoryVariants(isin, lang);

        } catch (error) {
          console.error('[IQ][CategoryForm] Error loading category data:', error);
        }
      } else {
        await getSearchConfigAsync();
      }
    }
  });
});

const initializeAvailableLanguages = async () => {
  try {
    const availableLangs = store.getters['userData/getAvailableLangs'];

    if (!availableLangs || availableLangs.length === 0) {
      try {
        await store.dispatch('userData/fetchLangsAsync');
      } catch (error) {
        console.warn('[IQ][CategoryForm] Could not fetch languages, using default set');
      }
    }

    const langs = store.getters['userData/getAvailableLangs'] || ['en-US', 'fr-FR'];

    if (langs && langs.length > 0) {
      finalAvailableLangs.value = langs;

      const currentLang = store.getters['userData/getDefaultLang'];
      recipeVariantLanguageList.value = langs
        .filter(langCode => langCode !== currentLang)
        .map(langCode => ({
          language: langCode,
          language_name: getLanguageName(langCode),
          languageFlag: getLanguageFlag(langCode)
        }));
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error initializing available languages:', error);

    const defaultLangs = ['en-US', 'fr-FR'];
    finalAvailableLangs.value = defaultLangs;
    const currentLang = store.getters['userData/getDefaultLang'] || 'fr-FR';

    recipeVariantLanguageList.value = defaultLangs
      .filter(langCode => langCode !== currentLang)
      .map(langCode => ({
        language: langCode,
        language_name: getLanguageName(langCode),
        languageFlag: getLanguageFlag(langCode)
      }));
  }
};

const loadCategoryVariants = async (isin, lang) => {
  try {
    const response = await fetchCategoryGroupData(isin, lang);
    if (response && response.data) {
      const { data } = response;
      selectedDefaultLang.value = Object.keys(data);

      selectedDefaultLang.value.forEach((language) => {
        if (data?.[language] && language !== lang) {
          const newVariantData = {
            name: data[language].name,
            lang: language,
          };

          recipeVariantList.value.push(newVariantData);
          initiallyVariantSupported.value.push(newVariantData);

          recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
            langItem => langItem.language !== language
          );
        }
      });
      if (recipeVariantList.value.length > 0) {
        await getCategoryAssociations();
      }
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading category variants:', error);
  }
};

onBeforeRouteLeave((_, __, next) => {
  if (hasChanges.value) {
    openModal({
      name: 'CategoryCancelModal',
      props: {
        availableLang: [],
        isCampaignModifiedFromShoppableReview: false,
        callConfirm: () => {
          closeModal('CategoryCancelModal');
          hasChanges.value = false;
          next();
        },
        closeModal: () => {
          closeModal('CategoryCancelModal');
          next(false);
        },
      },
    });
  } else {
    next();
  }
});

watch(categoriesName, (newName) => {
  if (newName && !categoriesSlug.value) {
    categoriesSlug.value = newName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
  hasChanges.value = true;
});
</script>

<style lang="scss" scoped>
@import '@/assets/scss/common/palettes';
@import '@/assets/scss/common/settings';
@import '@/assets/scss/common/fonts';

.category-item {
  font-family: $font-family-averta;

  .category-item-head {
    display: flex;
    align-items: flex-start;
    gap: 20px;

    .category-item-details {
      flex: 1;

      .category-item-details-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .category-item-details-body {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .category-item-details-text {
          margin-bottom: 12px;

          .category-item-details-text-mark {
            margin-left: 4px;
          }
        }
      }
    }
  }

  .category-item-hr {
    border: none;
    border-top: 1px solid $grainsboro;
    margin: 24px 0;
  }

  .category-item-settings {
    .category-item-settings-container {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .category-item-settings-actions {
        flex: 1;

        .slug-exist-main {
          margin-top: 4px;

          .slug-exist {
            color: $ruby-red;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.recipes-table-content {
  .content {
    .recipe-category {
      .recipe-header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .recipe-table-content {
        .add-zero-section {
          text-align: center;
          padding: 30px 20px;
          background-color: $white-smoke;
          border-radius: 8px;
          margin: 0 20px 20px 20px;

          .zero-promoted {
            .bold {
              font-weight: 700;
              color: $black;
              display: block;
              margin-bottom: 8px;
            }

            .normal {
              color: $grey;
              font-weight: 400;
            }
          }
        }

        .recipe-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 22px;

          .table-head {
            background-color: $white-smoke;
            border-radius: 8px 8px 0px 0px;

            .title {
              color: $spanish-gray;
              font-size: 14px;
              line-height: 1;
              text-align: left;
              margin: 0 4px;

              th {
                padding: 8px 12px;
                font-weight: 700;
                text-align: left;
              }

              .category-group-title {
                width: 318px;
              }

              .category-group-isin {
                width: 109px;
              }

              .category-group-total-time {
                width: 119px;
              }

              .ing-count {
                width: 230px;
              }
            }
          }

          .body {
            border-bottom: 1px solid $grainsboro;

            td {
              padding: 12px;
              vertical-align: middle;

              .recipe-number,
              .recipe-isin,
              .recipe-title,
              .recipe-time,
              .recipe-ingredients {
                font-size: 14px;
                color: $black;
              }
            }
          }
        }
      }
    }
  }
}


.recipe-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}
.recipe-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  display: flex;
  justify-content: space-evenly;
}
.category-recipes-wrapper, .promoted-recipes-wrapper, .category-recipes-combined-wrapper {
  padding: 0;
}
.category-recipes-combined-wrapper {
  margin-top: 20px;
}
.recipe-header-actions {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: end;
}
.category-variants-main {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  border-top: 1px solid $grainsboro;
}
.category-variant-card-main {
  width: 25%;
}
.recipe-header-text {
  width: 100%;
}



.delete-btn {
  .btn-reset {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    cursor: pointer;
    color: $ruby-red;
    font-size: 14px;
    font-weight: 500;

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .image {
      width: 16px;
      height: 16px;

      &.disabled {
        opacity: 0.5;
      }
    }

    .text {
      &.disabled {
        opacity: 0.5;
      }
    }

    &:hover:not(:disabled) {
      .text {
        text-decoration: underline;
      }
    }
  }
}
</style>

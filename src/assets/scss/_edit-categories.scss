.edit-categories-main-container {
  font-family: $font-family-averta;
  .background-image-categories {
    background: $sliver-whisper;
    height: 200px;
    position: relative;
    width: 100%;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    display: flex;

    .background-image {
      position: absolute;
      object-fit: cover;
      width: 100%;
      height: 100%;
      filter: brightness(60%) blur(12px);
    }

    .back-btn {
      top: 40px;
      cursor: pointer;
      position: absolute;
      left: 20px;

      .back-arrow-image {
        position: relative;
        top: -2px;
        width: 18px;
        height: 14px;
        cursor: pointer;
      }

      .back-to-categories {
        margin: 0px 4px;
        color: $green;
        cursor: pointer;
      }
    }

    .head-btn {
      position: absolute;
      right: 60px;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      top: 32px;
      gap: 20px;
    }

    .edit-cat-isin {
      color: $white;
      position: absolute;
      top: 82px;
      left: 26px;
    }
  }

  .input-categories-section {
    width: 100%;
    position: relative;
    cursor: default;
    margin-top: -90px;
    padding-left: 20px;
    padding-right: 60px;
    margin-bottom: -60px;

    .input-section {
      width: 100%;
      height: 100%;
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 8px;

      .input-sub-section {
        justify-content: space-between;
        display: flex;
        border-bottom: 1px solid $grainsboro;
      }

      .left-section {
        width: 770px;

        .image-section {
          cursor: pointer;
          width: 120px;
          height: 120px;
          float: left;
          margin: 20px;
          border-radius: 4px;
          overflow: hidden;
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;

          .hover-image {
            opacity: 0;
            width: 120px;
            height: 120px;

            &:hover {
              width: 120px;
              height: 120px;
              opacity: 1;
              background-image: url("@/assets/images/edit-image.png") !important;
              background-repeat: no-repeat;
              background-position: center;
              background-color: $translucent-black;
              border-radius: 4px;
              background-size: contain;
            }
          }
        }
        .image-main-div {
          width: 120px;
          height: 120px;
          position: relative;
          .display-image-section {
            width: 120px;
            height: 120px;
            object-fit: cover;
            position: absolute;
          }

          .image-inner-container {
            position: absolute;
            height: 120px;
            width: 100%;

            .progress-image {
              height: 120px;
              width: 100%;
              background-color: $jet-black;
              border-radius: 4px;
              position: relative;

              .progress-image-content {
                display: block !important;
                position: absolute;
                width: 100%;
                text-align: center;
                margin-top: 15px;

                .upload-text {
                  display: flex;
                  flex-direction: column;
                  margin-top: 11px;

                  .upload-heading {
                    height: 18px;
                    color: $white;
                  }

                  .upload-media {
                    height: 13px;
                    color: $white;
                  }
                }
              }
            }
          }
          .replace-image-tag {
            width: 120px;
            height: 120px;
            cursor: pointer;
            position: absolute;
            top: 0px;
            text-align: left;
            color: $white;
            .upload-input {
              width: 120px;
              height: 120px;
              opacity: 0;
            }
          }
        }

        .text-section {
          margin: 20px 1px 1px 1px;
          width: 70%;
          display: flex;
          float: none;
          position: relative;

          .compulsory-field-category {
            width: 9px;
            height: 10px;
            cursor: default;
            position: absolute;
            left: 286px;
            top: 4px;
          }

          .title {
            width: 100%;
            border: none;
            border-bottom: 2px dashed $sliver;
            background: $transparent;
            color: $black;
            padding: 5px 0 5px 0;
            text-overflow: ellipsis;
            cursor: text;
          }

          ::placeholder {
            color: $sliver;
            font-weight: 700;
          }
        }

        .slug-category-details {
          margin-top: 12px;
          display: flex;

          .slug-category-input {
            padding: 4px 0px;
            font-size: 16px;
            border: none;
            width: 63%;
            margin-left: 6px;
            border-bottom: 1px dashed $sliver;
          }

          .slug-exist-main {
            border-bottom: 1px dashed $sliver;

            .slug-exist {
              color: $ruby-red;
              position: relative;
              top: 6px;
            }
          }
        }

        .image-details {
          margin-top: 14px;

          .bold {
            color: $grey;
          }

          .normal {
            color: $grey;
          }

          .compulsory-field-category-image {
            position: relative;
            bottom: 6px;
            color: $fiery-red-blaze;
          }
        }
      }

      .right-section {
        position: relative;
        width: 200px;

        .published {
          opacity: 0.5;
          pointer-events: none;
        }

        .publish-btn {
          width: 130px;
          top: 20px;
          right: 25px;
          position: absolute;

          .text {
            position: relative;
            left: 14px;
            top: 4px;
            color: $black;
          }

          .switch {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 26px;
            margin-left: 20px;

            input {
              opacity: 0;
              width: 0;
              height: 0;
            }
          }

          .slider-round {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: $light-white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 30px;

            &:before {
              position: absolute;
              content: "";
              height: 23px;
              width: 23px;
              left: 2px;
              bottom: 2px;
              background-color: $white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 50%;
            }
          }

          input {
            &:checked {
              + {
                .slider-round {
                  background-color: $green;

                  &:before {
                    -webkit-transform: translateX(15px);
                    -ms-transform: translateX(15px);
                    transform: translateX(15px);
                  }
                }
              }
            }

            &:focus {
              + {
                .slider-round {
                  box-shadow: 0 0 1px $green;
                }
              }
            }
          }
        }

        .disable-categories {
          cursor: default;
        }

        .delete-btn {
          position: absolute;
          bottom: 16px;
          right: 0px;
          min-width: 160px;
          margin-right: 30px;

          .image {
            cursor: pointer;
            width: 14px;
            height: 16px;
          }

          .text {
            cursor: pointer;
            position: relative;
            top: 0px;
            margin-left: 6px;
            color: $fiery-red-blaze;
          }
          #disable-button {
            opacity: 0.5;
            pointer-events: none;
          }
          #enable-button {
            opacity: 1;
            pointer-events: all;
          }

          .disable-delete {
            opacity: 0.5;
            pointer-events: none;
          }

          .table-delete-btn {
            width: auto;
            height: 20px;
            padding: 0;
            cursor: pointer;
            margin: 0 auto;
            object-fit: cover;
            z-index: 1;
            position: relative;
            display: flex;
          }
        }
      }
    }

    .category-variant-section {
      padding: 25px;
      margin-top: -40px;

      .category-variants-main {
        display: flex;
        justify-content: space-between;
        margin-top: 38px;

        .category-variants {
          font-weight: 400;
          font-size: 20px;
          color: $black;
          display: flex;
        }

        .tag-variant-tooltip-section {
          display: flex;
          justify-content: flex-start;
        }

        .tooltip-main-container-for-tag-variant {
          position: relative;
          display: inline-block;

          .alert-image {
            height: 16px;
            width: 16px;
            margin-bottom: 3px;
            margin-left: 2px;
          }
          .add-variant-main {
            display: flex;
            position: relative;
            right: 8px;
            cursor: pointer;

            .add-variant-btn {
              height: 18px;
              width: 18px;
              margin-top: -1px;
            }

            .add-variant-text {
              font-size: 14px;
              font-weight: 700;
              text-transform: uppercase;
              color: $green-light;
              position: relative;
              top: 2px;
              left: 5px;
            }

            .disable-add-variant-main {
              opacity: 0.5;
            }
          }
        }

        .disable-add-variant-main {
          opacity: 0.5;
          pointer-events: none;
        }
        .add-variant-section {
          .add-variant-main {
            display: flex;
            position: relative;
            right: 8px;
            cursor: pointer;

            .add-variant-btn {
              height: 18px;
              width: 18px;
              margin-top: -1px;
            }

            .add-variant-text {
              text-transform: uppercase;
              color: $green-light;
              position: relative;
              top: 2px;
              left: 5px;
            }
            .disable-add-variant-main {
              opacity: 0.5;
            }
          }
        }
      }

      .add-category-variant {
        font-weight: 400;
        font-size: 16px;
        color: $grey;
        padding-top: 12px;
      }

      .category-variant-card-main {
        position: relative;
        width: 100%;
        display: grid;
        grid-template-columns: 25% 25% 25% 25%;
        grid-auto-rows: auto;
        padding-top: 12px;
      }
    }
  }

  .add-recipes-section {
    cursor: default;
    position: relative;
    padding-left: 20px;
    padding-right: 60px;
    width: 100%;
    height: 386px;
    margin-top: 80px;
    margin-bottom: 20px;

    .content {
      width: 100%;
      height: 100%;
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      position: relative;

      .left-section {
        position: absolute;
        top: 32%;
        width: 450px;
        margin-left: 44px;

        .head-text {
          font-weight: 400;
          font-size: 24px;
          color: $black;
          margin-bottom: 8px;
        }

        .sub-text {
          font-weight: 400;
          font-size: 16px;
          color: $grey;
          margin-bottom: 30px;
        }
      }

      .right-section {
        position: absolute;
        width: 291px;
        right: 0;
        margin-top: 50px;
        margin-right: 110px;

        .image-content {
          width: 291px;
          height: 291px;

          .image {
            width: 291px;
            height: 291px;
          }
        }
      }
    }
  }

  .recipes-table-content {
    cursor: default;
    position: relative;
    padding-left: 20px;
    padding-right: 60px;
    width: 100%;
    height: 100%;
    margin-top: 80px;
    margin-bottom: 40px;

    .content {
      width: 100%;
      height: 100%;
      background-color: $white;
      border-radius: 8px;
      position: relative;

      .loading {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 255px;
        padding: 0 20px;

        .input-loading {
          height: 60px;
          display: flex;
          justify-content: center;

          .loader-image {
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 20px;
            height: 20px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
          }
        }

        .loader-text-container {
            display: flex;
            justify-content: center;

        .loading-text {
            background-color: $green-peppermint;
            border-radius: 4px;
            border: 1px solid $green-fringy-flower;
            width: 468px;
            height: 57px;

          p {
            font-weight: 400;
            font-size: 16px;
            color: $shadow-gray;
            padding: 18px 0px;
            text-align: center;
          }
        }
      }
    }

      .promoted-header {
        margin-top: 25px;
        margin-left: 20px;
        font-size: 24px;
        font-weight: 400;
        color: $black;
      }

      .promoted-subtitle {
        margin-left: 20px;
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 400;
        color: $grey;
      }

      .add-zero-section {
        width: 100%;
        padding: 0 20px;
        margin-bottom: 42px;

        .zero-promoted {
          width: 100%;
          text-align: center;
          margin: 0 auto;
          padding: 29px 0;
          background-color: $pearl-mist;
          border: 1px solid $grainsboro;
          border-radius: 4px;

          .bold {
            color: $spanish-gray;
          }

          .normal {
            color: $spanish-gray;
          }
        }
      }

      .category-recipe-section {
        margin: 40px 0;
      }

      .promote-table-content {
        margin-bottom: 42px;

        .table-header {
          color: $spanish-gray;
          background-color: $white-smoke;
          display: flex;
          flex-direction: row;

          .margin-div {
            width: 10%;
          }

          .category-group-isin {
            width: 11%;
          }

          .category-group-total-time {
            width: 12%;
          }

          .category-group-title {
            width: 30%;
          }

          div {
            padding: 8px 0;
          }
        }

        .promote-table {
          .all-content-categories {
            .body {
              display: flex;
              align-items: center;
              position: relative;

              .draggable-icon {
                position: absolute;
                top: -5px;
                left: 48%;
                visibility: hidden;
                cursor: all-scroll;
              }
            }

            .body:hover {
              background-color: $light-mint;
              border: 1px solid $green;

              .draggable-icon {
                visibility: visible;
              }

              .menu-container {
                background-color: $transparent;
              }
            }
          }
          .table-head {
            background-color: $white-smoke;
            border-radius: 8px 8px 0px 0px;

            .title {
              color: $spanish-gray;
              font-size: 14px;
              line-height: 1;
              text-align: left;
              margin: 0 4px;

              th {
                padding: 8px 0;
                font-weight: 700;
              }

              .category-group-isin {
                width: 109px;
              }

              .ing-count {
                width: 157px;
              }
            }
          }

          .body {
            border-bottom: 1px solid $grainsboro;

            .checkbox-promote {
              width: 14px;
              margin-left: 20px;

              .container-promote {
                top: -10px;
                display: block;
                position: relative;
                cursor: pointer;
                font-size: 22px;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;

                input {
                  position: absolute;
                  opacity: 0;
                  cursor: pointer;
                  height: 0;
                  width: 0;

                  &:checked {
                    ~ {
                      .checkmark {
                        background-color: $green-light;

                        &:after {
                          display: block;
                        }
                      }
                    }
                  }
                }

                .checkmark {
                  &:after {
                    left: 7px;
                    top: 1px;
                    width: 8px;
                    height: 16px;
                    border: solid $white;
                    border-width: 0 2px 2px 0;
                    -webkit-transform: rotate(45deg);
                    -ms-transform: rotate(45deg);
                    transform: rotate(45deg);
                  }
                }
              }

              .checkmark {
                border-radius: 4px;
                position: absolute;
                top: 0;
                left: 0;
                height: 22px;
                width: 22px;
                background-color:$gentle-whisper-gray;

                &:after {
                  content: "";
                  position: absolute;
                  display: none;
                }
              }
            }

            .image-promote {
              width: 60px;
              height: 60px;
              border-radius: 4px;
              margin: 10px 18px;
              overflow: hidden;

              .image {
                object-fit: cover;
                width: 100%;
                height: 60px;
              }
            }

            .table-image-promote {
              width: 10%;
            }

            .table-promote-code {
              width: 11%;
            }

            .promote-recipe-name {
              position: relative;
              width: 30%;
            }

            .promote-ingredient-time {
              width: 12%;
            }

            .promote-ingredient-count {
              width: 31%;
            }

            .promote-code {
              font-size: 12px;
              font-weight: 400;
              color: $stone-gray;
            }

            .promote-name {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              max-width: 70%;
              color: $black;
              margin-top: 4px;
              margin-right: 4px;
            }

            .promote-subtitle {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              margin-top: 3px;
              max-width: 70%;
              color: $shadow-gray;
              font-weight: 400;
              font-size: 14px;
            }

            .promote-details {
              .details {
                padding-left: 4px;
                display: inline-block;
                width: 102px;
                font-size: 14px;
                font-weight: 400;
                color: $black;
              }

              .small {
                width: 88px;
              }
            }

            .promote-btn {
              width: 142px;
              padding-left: 12px;

              .promoted-products-btn {
                background-color: $white;
                font-size: 14px;
                border: none;
                font-weight: bold;
                cursor: pointer;
              }
            }

            .menu {
              position: relative;
              margin-right: 18px;

              .menu-container {
                background-color: $white;
                border-radius: 10px;
                width: 28px;
                height: 20px;
                cursor: pointer;
                display: flex;
                align-items: center;

                .table-edit-btn {
                  width: 17px;
                  height: 5px;
                  padding: 0;
                  margin: 0 auto;
                  object-fit: cover;
                  z-index: 1;
                }

                &:hover {
                  background-color: $pure-white;
                }
              }

              .menu-selected {
                background-color: $aqua-spring;

                &:hover {
                  background-color: $aqua-spring;
                }
              }

              .menu-box {
                display: block;
                position: absolute;
                right: 10px;
                width: 151px;
                top: 24px;
                z-index: 2;
                box-shadow: 0 4px 10px 0 $shadow-black,
                  0 3px 5px 0 $faint-black,
                  0 0 0 1px $shadowy-black;
                border-radius: 4px;
                background: $white;

                .menu-list {
                  list-style: none;
                  background: $white;
                  border-radius: 8px;
                  margin: 11px 5px;

                  li {
                    display: flex;
                    align-items: center;
                    height: 30px;
                    width: 141px;
                    font-size: 16px;
                    color: $black;
                    font-weight: 700;
                    padding-left: 10px;

                    &:hover {
                      color: $white;
                      background: $green;
                      cursor: pointer;
                    }
                  }

                  .hide-data {
                    display: none;
                  }
                }
              }
            }
          }

          .body:first-child {
            .disable-for-first-row {
              opacity: 0.5;
              pointer-events: none;
            }
          }

          .body:last-child {
            .disable-for-last-row {
              opacity: 0.5;
              pointer-events: none;
            }
          }
        }
      }

      .margin-promote-table {
        margin-bottom: 0px;
      }

      .recipe-category {
        .recipe-header-section {
          width: 100%;
          justify-content: space-between;
          display: flex;
          margin-bottom: 20px;

          .recipe-header {
            margin-left: 20px;
            font-size: 24px;
            font-weight: 400;
            color: $black;
          }

          .search-section {
            justify-content: space-between;
            display: flex;

            .search-box {
              position: relative;
              background-color: $pearl-mist;
              border: 1px solid $grainsboro;
              border-radius: 30px;
              margin-right: 20px;
              padding: 0 12px 0 16px;
              height: 36px;
              width: 300px;

              .search-input-box {
                width: 250px;
                height: 34px;
                margin: 0px 0px 0px 20px;
                padding: 0;
                background: none;
                color: $black;
                border: none;
                font-size: 16px;
                border-radius: 0;
                box-shadow: none;

                ::placeholder {
                  font-size: 16px;
                  color: $graphite-gray;
                  font-weight: 400;
                }
              }

              .align-search-input-box {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 215px;
                display: block;
              }

              .search-icon-green-image {
                position: relative;
                top: -25px;
                float: left;
                height: 18px;
                width: 18px;
                cursor: pointer;
              }

              .exit-search-icon {
                width: 16px;
                height: 16px;
                position: relative;
                top: -27px;
                left: 230px;
                cursor: pointer;
              }
            }

            .add-btn {
              cursor: pointer;
              max-width: 120px;
              height: 24px;
              margin-right: 20px;
              position: relative;
              top: 6px;

              .add-image {
                width: 18px;
                height: 18px;
              }

              .text {
                position: relative;
                top: 1px;
                font-size: 14px;
                font-weight: 700;
                color: $green;
              }
            }
          }
        }

        .recipe-table-content {
          .no-result-for-category {
            display: flex;
            justify-content: space-around;
            margin-top: 14px;
            font-weight: 700;
            font-size: 16px;
            color: $shadow-gray;
            padding: 30px;
          }

          .recipe-table {
            margin-bottom: 22px;

            .table-head {
              background-color: $white-smoke;
              border-radius: 8px 8px 0px 0px;

              .title {
                color: $spanish-gray;
                font-size: 14px;
                line-height: 1;
                text-align: left;
                margin: 0 4px;

                th {
                  padding: 8px 0;
                  font-weight: 700;
                }

                .category-group-title {
                  width: 318px;
                }

                .category-group-isin {
                  width: 109px;
                }

                .category-group-total-time {
                  width: 119px;
                }

                .ing-count {
                  width: 230px;
                }
              }
            }

            .body {
              border-bottom: 1px solid $grainsboro;

              .checkbox-recipe {
                width: 14px;
                margin-left: 20px;

                .container-recipe {
                  top: -10px;
                  display: block;
                  position: relative;
                  cursor: pointer;
                  font-size: 22px;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;

                  input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;

                    &:checked {
                      ~ {
                        .checkmark {
                          background-color: $green-light;

                          &:after {
                            display: block;
                          }
                        }
                      }
                    }
                  }

                  .checkmark {
                    &:after {
                      left: 7px;
                      top: 1px;
                      width: 8px;
                      height: 16px;
                      border: solid $white;
                      border-width: 0 2px 2px 0;
                      -webkit-transform: rotate(45deg);
                      -ms-transform: rotate(45deg);
                      transform: rotate(45deg);
                    }
                  }
                }

                .checkmark {
                  border-radius: 4px;
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 22px;
                  width: 22px;
                  background-color: $gentle-whisper-gray;

                  &:after {
                    content: "";
                    position: absolute;
                    display: none;
                  }
                }
              }

              .image-recipe {
                width: 60px;
                height: 60px;
                overflow: hidden;
                border-radius: 4px;
                margin: 10px 18px;

                .image {
                  object-fit: cover;
                  width: 100%;
                  height: 60px;
                }
              }

              .table-image-recipe {
                width: 1%;
              }

              .table-recipe-code {
                width: 11%;
              }

              .table-recipe-time {
                width: 12%;
              }

              .table-recipe-name {
                position: relative;
                width: 41%;
              }

              .recipe-code {
                font-size: 12px;
                font-weight: 400;
                color: $stone-gray;
              }

              .recipe-name {
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                font-size: 14px;
                font-weight: 700;
                max-width: 70%;
                color: $black;
                margin-top: 2px;
                margin-right: 4px;
              }

              .recipe-subtitle {
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                margin-top: 3px;
                color: $shadow-gray;
                font-weight: 400;
                font-size: 14px;
                max-width: 70%;
              }

              .recipe-details {
                .details {
                  padding-left: 4px;
                  display: inline-block;
                  width: 102px;
                  font-size: 14px;
                  font-weight: 400;
                  color: $black;
                }

                .small {
                  width: 88px;
                }
              }

              .recipe-btn {
                width: 120px;
                margin-right: 22px;
                position: relative;
              }

              .menu {
                position: relative;
                margin-right: 18px;

                .menu-container {
                  background-color: $white;
                  border-radius: 10px;
                  width: 28px;
                  height: 20px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;

                  .table-edit-btn {
                    width: 17px;
                    height: 5px;
                    padding: 0;
                    margin: 0 auto;
                    object-fit: cover;
                    z-index: 1;
                  }

                  &:hover {
                    background-color: $pure-white;
                  }
                }

                .menu-selected {
                  background-color: $aqua-spring;

                  &:hover {
                    background-color: $aqua-spring;
                  }
                }

                .menu-box {
                  display: block;
                  position: absolute;
                  right: 10px;
                  width: 151px;
                  top: 24px;
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 4px;
                  background: $white;

                  .menu-list {
                    list-style: none;
                    background: $white;
                    border-radius: 8px;
                    margin: 11px 5px;

                    li {
                      display: flex;
                      align-items: center;
                      height: 30px;
                      width: 141px;
                      font-size: 16px;
                      color: $black;
                      font-weight: 700;
                      padding-left: 10px;

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }

                    .hide-data {
                      display: none;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .recipes-table-content-data {
    margin-top: 20px;
  }

  .edit-categories-delete-modal {
    text-align: left;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    align-items: normal;
    margin-top: 30px;
    max-height: 160px;
    padding: 0 14px;
    width: 450px;

    .delete-image {
      img {
        width: 80px;
        margin-bottom: 10px;
      }
    }

    .delete-content {
      width: 310px;

      .delete-title {
        position: relative;
        left: -8px;
        color: $black;
        font-weight: bold;
        font-size: 20px;
      }

      .delete-description {
        position: relative;
        left: -8px;
        color: $grey;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 32px;
        margin-bottom: 8px;
        gap: 20px;
      }
    }
  }

  .edit-categories-delete-modal-recipe {
    text-align: left;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    align-items: normal;
    margin-top: 30px;
    max-height: 160px;
    padding: 0 14px;
    width: 450px;

    .delete-image {
      img {
        width: 80px;
        margin-bottom: 10px;
      }
    }

    .delete-content {
      width: 310px;

      .delete-title {
        position: relative;
        left: -8px;
        color: $black;
        font-weight: bold;
        font-size: 20px;
      }

      .delete-description {
        position: relative;
        left: -8px;
        color: $grey;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 32px;
        margin-bottom: 8px;
        gap: 20px;
      }
    }
  }

  .add-recipe-matches-modal {
    padding: 0;
    width: 838px;
    height: 100%;
    text-align: left;
    overflow-y: visible !important;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;

    ::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    ::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .title-section {
      justify-content: space-between;
      display: flex;
      padding-left: 16px;
      padding-right: 4px;
      margin-top: 6px;
      margin-bottom: 20px;

      .title {
        font-size: 24px;
        color: $black;
        font-weight: 700;
      }

      .search-box-pop {
        position: relative;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box {
          width: 250px;
          height: 34px;
          margin: 0px 0px 0px 22px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
        }

        .search-icon-green-image {
          position: relative;
          top: -28px;
          left: -5px;
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon {
          width: 12px;
          height: 12px;
          position: relative;
          top: -24px;
          float: right;
          cursor: pointer;
        }
      }
    }

    .add-table-content {
      position: relative;
      left: 10px;
      max-height: 243px;
      min-height: 243px;
      overflow-y: scroll;
      padding-left: 6px;
      padding-right: 22px;

      .table-image-loader {
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9;
        background-color: $white;
        margin: 0 auto;
        height: 200px;
        width: 210px;

        .loader {
          border: 3px solid $pristine-white;
          border-radius: 50%;
          border-top: 3px solid $spanish-gray;
          border-right: 3px solid $spanish-gray;
          border-bottom: 3px solid $spanish-gray;
          width: 24px;
          height: 24px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }

        @-webkit-keyframes spin {
          0% {
            -webkit-transform: rotate(0deg);
          }

          100% {
            -webkit-transform: rotate(360deg);
          }
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }

      .add-table {
        .no-recipe-result {
          position: relative;
          top: 90px;
          display: flex;
          justify-content: center;
          font-weight: 700;
          font-size: 20px;
          color: $shadow-gray;
        }

        .add-recipe-body {
          border-bottom: 1px solid $grainsboro;

          .image-recipe {
            width: 60px;
            height: 60px;
            margin-top: 10px;
            margin-bottom: 10px;
            margin-right: 20px;
            overflow: hidden;

            .image {
              width: 100%;
              height: 60px;
              object-fit: cover;
            }
          }

          .table-image-recipe {
            width: 70px;
          }

          .table-recipe-code {
            width: 70px;
          }

          .recipe-code {
            font-size: 12px;
            font-weight: 400;
            color: $stone-gray;
          }

          .recipe-name {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 14px;
            font-weight: 700;
            max-width: 310px;
            color: $black;
          }

          .recipe-subtitle {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-top: 3px;
            color: $shadow-gray;
            font-weight: 400;
            font-size: 14px;
          }

          .recipe-details {
            .details {
              padding-left: 4px;
              width: 150px;
              font-size: 14px;
              font-weight: 400;
              color: $black;
            }

            .image {
              position: relative;
              top: -1px;
              width: 12px;
              height: 12px;
            }
          }
        }

        .add-recipe-body:first-child {
          border-top: 1px solid $grainsboro;
        }
      }

      .load-button {
        padding: 15px 0px;
        width: 140px;
        margin: 0 auto;
      }
    }

    .done-section {
      text-align: center;
      border-top: 1px solid $grainsboro;

      .done-btn {
        margin-top: 14px;
        background-color: $green;
        color: $white;
        font-size: 16px;
        padding: 12px 37px;
        font-weight: bold;
        cursor: pointer;
        border-radius: 50px;
        border: none;
        box-shadow: 0px 1px 5px 0px $shadow-black;
      }
    }
  }

  .category-recipe-variant-modal {
    min-width: 560px;
    height: 260px;

    .recipe-variant {
      display: flex;
      justify-content: space-between;
      padding: 22px 28px;

      span {
        font-size: 24px;
        color: $black;
        font-weight: 700;
      }

      img {
        height: 24px;
        width: 24px;
        cursor: pointer;
      }
    }

    .recipe-variant-language-dropdown {
      margin-top: 20px;
      cursor: pointer;

      .recipe-variant-selected-language {
        display: flex;
        justify-content: space-between;
        margin: 10px 40px 10px 33px;
        min-height: 50px;
        border: 1px solid $grainsboro;
        border-radius: 8px;
        align-items: center;
        padding: 0px 15px 0px 10px;

        span {
          display: flex;
          align-items: center;

          img {
            height: 20px;
            width: 30px;
          }

          p {
            font-size: 16px;
            font-weight: 400;
            color: $black;
            margin-left: 12px;
          }
        }

        .dropdown-icon {
          transform: rotate(90deg);
          width: 6px;
          height: 10px;
          cursor: pointer;

          &.dropdown-disabled {
            cursor: default;
            pointer-events: none;
          }
        }
      }

      .autocomplete-results {
        position: absolute;
        list-style: none;
        left: 10px;
        right: 0px;
        box-shadow: 0 1px 10px 0 $box-shadow;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        overflow-y: scroll;
        width: 84%;
        border-radius: 8px;
        scrollbar-width: none;
        text-align: left;
        margin-left: 33px;
        height: max-content;
        max-height: 115px;

        &::-webkit-scrollbar {
          display: none;
        }

        .recipe-language-list {
          display: flex;

          span {
            display: flex;

            img {
              height: 20px;
              width: 30px;
            }

            p {
              font-size: 16px;
              font-weight: 400;
              color: $black;
              margin-left: 12px;
            }
          }
        }
      }

      .autocomplete-result {
        padding: 8px 10px;
        cursor: pointer;
        margin: 2px;
        border-radius: 4px;

        &.is-active,
        &:hover {
          background: $green;

          p {
            color: $white;
          }
        }
      }
    }

    .recipe-variant-next-button {
      display: flex;
      justify-content: center;
      margin-top: 50px;

      .next-button {
        background: $green-light;
        padding: 14px 48px;
        border-radius: 50px;
        border: none;
        cursor: pointer;

        p {
          color: $white;
          font-size: 14px;
          font-weight: 900;
        }
      }

      .disable-next-button {
        opacity: 0.3;
        pointer-events: none;
        cursor: default;
      }
    }
  }
}

.edit-category-recipe-name-tooltip {
  display: flex;
  justify-content: flex-start;
}

.slug-warnings-categories {
  font-family: $font-family-averta;
  color: $ruby-red;
  font-size: 12px;
  text-align: left;
  font-weight: 400;
  margin-top: 10px;
}

.tooltip-main-container-edit-category {
  display: inline-block;
  position: relative;
  font-family: $font-family-averta;
  top: -2px;
  margin-left: 4px;
  margin-right: 20px;
  min-width: 28px;
  img {
    height: 16px;
    min-width: 16px;
  }
}

.disable-table-data {
  opacity: 0.3;
  pointer-events: none;
}

.edit-category-selection-container {
  font-family: $font-family-averta;
  .edit-category-selection-panel {
    display: flex;
    align-items: center;
    margin: 15px 0px;
    position: relative;
    padding-left: 12px;

    .edit-category-select-all-text {
      position: absolute;
      left: 49px;
      margin-left: 10px;
      color: $black-bean;
      cursor: pointer;
    }

    .edit-category-selection {
      display: flex;
      width: 58%;

      .edit-category-selected-text {
        font-size: 16px;
        font-weight: 400;
        color: $black;
        position: absolute;
        left: 136px;
        bottom: 4px;

        .edit-category-selected-cross-icon {
          width: 10px;
          height: 10px;
          margin-left: 13px;
        }

        .edit-category-selected-cross-icon:hover {
          cursor: pointer;
        }
      }
    }

    .edit-category-btn-container {
      display: flex;
      position: absolute;
      right: 247px;
      align-items: center;
    }

    .edit-category-cancel-btn {
      color: $kelly-green;
      width: 57px;
      height: 18px;
      position: absolute;
      right: 65px;
      border: none;
      background: none;
    }

    .edit-category-cancel-btn:hover {
      cursor: pointer;
    }

    .edit-category-select-all-checbox-section {
      width: 4%;
      margin-left: 9px;
      padding-top: 4px;

      .edit-category-checkbox-section {
        display: block;
        position: relative;
        font-size: 22px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        width: 24px;
        height: 24px;

        input {
          opacity: 0;
          height: 0;
          width: 0;

          &:checked {
            ~ {
              .checkmark {
                background-color: $green-light;
                border: 3px solid $green-light;

                &:after {
                  display: block;
                }
              }
            }
          }
        }

        .checkmark {
          position: absolute;
          top: -3px;
          left: 0;
          height: 24px;
          width: 24px;
          color: $grainsboro;
          background-color: $white;
          border: 3px solid $grainsboro;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            border: 3px solid $green-light;
          }

          &:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 12px;
            border: solid $white;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
            font-weight: 800;
          }
        }

        .edit-category-select-all-recipes {
          display: flex;
          font-size: 14px;
          font-weight: 700;
          color: $black $jet-black;
          cursor: pointer;
        }
      }
    }
  }
}

.recipe-selected-color-category {
  background: $aqua-spring !important;
}

.edit-category-product-table-srno-checkbox {
  font-family: $font-family-averta;
  font-weight: 400;
  color: $black;
  font-size: 14px;
  display: grid;
  align-content: center;
  padding-top: 15px;
  margin-right: 10px;
  margin-left: 9px;

  .edit-category-select-all-checbox-section {
    width: 2%;

    .edit-category-checkbox-section {
      display: block;
      position: relative;
      font-size: 22px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      input {
        opacity: 0;
        height: 0;
        width: 0;

        &:checked {
          ~ {
            .checkmark {
              background-color: $green-light;
              border: 3px solid $green-light;

              &:after {
                display: block;
              }
            }
          }
        }
      }

      .checkmark {
        position: absolute;
        top: -3px;
        left: 0;
        height: 24px;
        width: 24px;
        color: $grainsboro;
        background-color: $white;
        border: 3px solid $grainsboro;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          border: 3px solid $green-light;
        }

        &:after {
          content: "";
          position: absolute;
          display: none;
          left: 6px;
          top: 2px;
          width: 6px;
          height: 12px;
          border: solid $white;
          border-width: 0 2px 2px 0;
          -webkit-transform: rotate(45deg);
          -ms-transform: rotate(45deg);
          transform: rotate(45deg);
          font-weight: 800;
        }
      }

      .edit-category-select-all-recipes {
        display: flex;
        font-size: 14px;
        font-weight: 700;
        color: $black $jet-black;
        cursor: pointer;
      }
    }
  }
}

.edit-category-select-button {
  justify-content: end;
  margin-top: 12px;
  font-family: $font-family-averta;
  color: $kelly-green;
  margin-right: 30px;

  span:hover {
    cursor: pointer;
  }
}
.add-recipes-section {
  margin-top: 20px;
}
.add-recipes-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 300px;

  .add-recipes-left {
    flex: 1;
    max-width: 450px;

    .add-recipes-header {
      margin-bottom: 30px;

      .add-recipes-title {
        font-size: 24px;
        font-weight: 400;
        color: $black;
        margin-bottom: 8px;
      }

      .add-recipes-subtitle {
        font-size: 16px;
        font-weight: 400;
        color: $grey;
      }
    }

    .add-recipes-actions {
      .add-recipes-btn {
        background-color: $green;
        color: $white;
        font-size: 16px;
        padding: 12px 24px;
        font-weight: 700;
        cursor: pointer;
        border-radius: 50px;
        border: none;
        box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.2);
        transition: background-color 0.3s ease;

        &:hover {
          background-color: darken($green, 10%);
        }

        &:active {
          transform: translateY(1px);
        }
      }
    }
  }

  .add-recipes-right {
    flex: 0 0 auto;
    margin-left: 40px;

    .add-recipes-illustration {
      .pan-image {
        max-width: 291px;
        height: auto;
        object-fit: contain;
      }
    }
  }
}
